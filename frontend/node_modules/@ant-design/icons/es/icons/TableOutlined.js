function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TableOutlinedSvg from "@ant-design/icons-svg/es/asn/TableOutlined";
import AntdIcon from "../components/AntdIcon";
const TableOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: TableOutlinedSvg
}));

/**![table](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAxNjBIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTkyYzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgMjA4SDY3NlYyMzJoMjEydjEzNnptMCAyMjRINjc2VjQzMmgyMTJ2MTYwek00MTIgNDMyaDIwMHYxNjBINDEyVjQzMnptMjAwLTY0SDQxMlYyMzJoMjAwdjEzNnptLTQ3NiA2NGgyMTJ2MTYwSDEzNlY0MzJ6bTAtMjAwaDIxMnYxMzZIMTM2VjIzMnptMCA0MjRoMjEydjEzNkgxMzZWNjU2em0yNzYgMGgyMDB2MTM2SDQxMlY2NTZ6bTQ3NiAxMzZINjc2VjY1NmgyMTJ2MTM2eiIgLz48L3N2Zz4=) */
const RefIcon = /*#__PURE__*/React.forwardRef(TableOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TableOutlined';
}
export default RefIcon;