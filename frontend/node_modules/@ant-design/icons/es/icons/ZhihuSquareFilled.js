function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ZhihuSquareFilledSvg from "@ant-design/icons-svg/es/asn/ZhihuSquareFilled";
import AntdIcon from "../components/AntdIcon";
const ZhihuSquareFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: ZhihuSquareFilledSvg
}));

/**![zhihu-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNDMyLjMgNTkyLjhsNzEgODAuN2M5LjIgMzMtMy4zIDYzLjEtMy4zIDYzLjFsLTk1LjctMTExLjl2LS4xYy04LjkgMjktMjAuMSA1Ny4zLTMzLjMgODQuNy0yMi42IDQ1LjctNTUuMiA1NC43LTg5LjUgNTcuNy0zNC40IDMtMjMuMy01LjMtMjMuMy01LjMgNjgtNTUuNSA3OC04Ny44IDk2LjgtMTIzLjEgMTEuOS0yMi4zIDIwLjQtNjQuMyAyNS4zLTk2LjhIMjY0LjFzNC44LTMxLjIgMTkuMi00MS43aDEwMS42Yy42LTE1LjMtMS4zLTEwMi44LTItMTMxLjRoLTQ5LjRjLTkuMiA0NS00MSA1Ni43LTQ4LjEgNjAuMS03IDMuNC0yMy42IDcuMS0yMS4xIDAgMi42LTcuMSAyNy00Ni4yIDQzLjItMTEwLjcgMTYuMy02NC42IDYzLjktNjIgNjMuOS02Mi0xMi44IDIyLjUtMjIuNCA3My42LTIyLjQgNzMuNmgxNTkuN2MxMC4xIDAgMTAuNiAzOSAxMC42IDM5aC05MC44Yy0uNyAyMi43LTIuOCA4My44LTUgMTMxLjRINTE5czEyLjIgMTUuNCAxMi4yIDQxLjdoLTExMGwtLjEgMS41Yy0xLjUgMjAuNC02LjMgNDMuOS0xMi45IDY3LjZsMjQuMS0xOC4xem0zMzUuNSAxMTZoLTg3LjZsLTY5LjUgNDYuNi0xNi40LTQ2LjZoLTQwLjFWMzIxLjVoMjEzLjZ2Mzg3LjN6TTQwOC4yIDYxMXMwLS4xIDAgMHptMjE2IDk0LjNsNTYuOC0zOC4xaDQ1LjYtLjFWMzY0LjdINTk2Ljd2MzAyLjVoMTQuMXoiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(ZhihuSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ZhihuSquareFilled';
}
export default RefIcon;