function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UpSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/UpSquareTwoTone";
import AntdIcon from "../components/AntdIcon";
const UpSquareTwoTone = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: UpSquareTwoToneSvg
}));

/**![up-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMTQzLjUtMjI4LjdsMTc4LTI0NmMzLjItNC40IDkuNy00LjQgMTIuOSAwbDE3OCAyNDZjMy45IDUuMy4xIDEyLjctNi40IDEyLjdoLTQ2LjljLTEwLjIgMC0xOS45LTQuOS0yNS45LTEzLjJMNTEyIDQ2NS40IDQwNi44IDYxMC44Yy02IDguMy0xNS42IDEzLjItMjUuOSAxMy4ySDMzNGMtNi41IDAtMTAuMy03LjQtNi41LTEyLjd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik0zMzQgNjI0aDQ2LjljMTAuMyAwIDE5LjktNC45IDI1LjktMTMuMkw1MTIgNDY1LjRsMTA1LjIgMTQ1LjRjNiA4LjMgMTUuNyAxMy4yIDI1LjkgMTMuMkg2OTBjNi41IDAgMTAuMy03LjQgNi40LTEyLjdsLTE3OC0yNDZhNy45NSA3Ljk1IDAgMDAtMTIuOSAwbC0xNzggMjQ2Yy0zLjggNS4zIDAgMTIuNyA2LjUgMTIuN3oiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
const RefIcon = /*#__PURE__*/React.forwardRef(UpSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpSquareTwoTone';
}
export default RefIcon;